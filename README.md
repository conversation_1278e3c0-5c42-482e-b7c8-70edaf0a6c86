# TVBrowser - Apple TV Web Browser

A full-featured web browser designed specifically for Apple TV, built with SwiftUI and optimized for the Apple TV remote experience.

## Features

### 🌐 Core Browsing
- **WebKit Integration**: Full web browsing capabilities using WKWebView
- **Navigation Controls**: Back, forward, refresh, and stop functionality
- **URL Input**: Custom URL input interface optimized for Apple TV remote
- **Smart URL Handling**: Automatic HTTPS addition and Google search for non-URLs

### 📑 Tab Management
- **Multiple Tabs**: Support for multiple browser tabs
- **Tab Switching**: Easy navigation between open tabs
- **New Tab Creation**: Quick access to create new tabs

### ⭐ Bookmarks
- **Visual Bookmark Grid**: Beautiful grid layout for easy browsing
- **Quick Access**: One-click access to favorite websites
- **Default Bookmarks**: Pre-loaded with popular websites (Google, YouTube, Netflix, etc.)
- **Bookmark Management**: Add, remove, and organize bookmarks
- **Smart Icons**: Automatic icon selection based on website

### 📚 History
- **Browse History**: Automatic tracking of visited websites
- **History Management**: View and clear browsing history
- **Recent Items**: Quick access to recently visited sites

### ⚙️ Settings
- **Homepage Configuration**: Set custom homepage
- **Search Engine Selection**: Choose from Google, <PERSON>, DuckDuckGo, Yahoo
- **Privacy Controls**: Clear history on exit, block pop-ups
- **JavaScript Toggle**: Enable/disable JavaScript execution

### 🎮 Apple TV Optimized
- **Remote Navigation**: Fully optimized for Apple TV remote control
- **Focus Management**: Intuitive focus-based navigation
- **Large Touch Targets**: All buttons and controls sized for TV interaction
- **Quick Links**: Fast access to popular websites

## Architecture

### Core Components

1. **ContentView**: Main app interface coordinator
2. **BrowserView**: WebKit integration and web page rendering
3. **NavigationBarView**: URL bar and navigation controls
4. **TabManager**: Tab creation, switching, and management
5. **BookmarkManager**: Bookmark storage and organization
6. **HistoryManager**: Browse history tracking
7. **URLInputView**: Custom URL input interface
8. **BookmarkGridView**: Visual bookmark interface
9. **SettingsView**: App configuration and preferences

### Data Management

- **UserDefaults**: Persistent storage for bookmarks, history, and settings
- **ObservableObject**: Reactive data binding throughout the app
- **JSON Encoding**: Efficient serialization of bookmarks and history

## Usage

### Navigation
- Use the Apple TV remote to navigate between interface elements
- Click the trackpad to select buttons and links
- Swipe to scroll through web pages and bookmark grids

### Entering URLs
1. Click the URL bar in the navigation area
2. Use the on-screen interface to enter URLs or search terms
3. Select from quick links for popular websites
4. Press "Go" or use the remote's select button

### Managing Bookmarks
1. While browsing, click the heart icon to bookmark the current page
2. Access bookmarks by clicking the grid icon in the navigation bar
3. Click any bookmark to navigate to that website
4. Long-press bookmarks to delete them

### Tab Management
1. Click the "+" button to create a new tab
2. Navigate between tabs using the tab switcher
3. Close tabs by swiping or using the close button

### Settings
1. Click the gear icon to access settings
2. Configure homepage, search engine, and privacy options
3. Reset to defaults if needed

## Technical Requirements

- **Platform**: tvOS 18.4+
- **Framework**: SwiftUI
- **Language**: Swift 5.0+
- **Dependencies**: WebKit framework

## Installation

1. Open the project in Xcode
2. Select an Apple TV simulator or connected Apple TV device
3. Build and run the project (⌘+R)

## Development Notes

### Focus Management
The app uses SwiftUI's focus system to ensure proper navigation with the Apple TV remote. All interactive elements are properly configured for focus-based navigation.

### Performance Optimization
- Lazy loading for bookmark grids
- Efficient memory management for web views
- Optimized image handling for thumbnails

### Privacy & Security
- Secure HTTPS enforcement
- JavaScript control options
- History clearing capabilities
- No data collection or tracking

## Future Enhancements

- [ ] Tab thumbnails and visual tab switching
- [ ] Download manager
- [ ] Reading mode
- [ ] Password manager integration
- [ ] Parental controls
- [ ] Custom themes
- [ ] Gesture shortcuts
- [ ] Voice search integration

## License

This project is created for educational and personal use. Please ensure compliance with Apple's App Store guidelines if distributing.

## Support

For issues or feature requests, please refer to the project documentation or create an issue in the project repository.
