//
//  BookmarkGridView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct BookmarkGridView: View {
    @ObservedObject var bookmarkManager: BookmarkManager
    @ObservedObject var tabManager: TabManager
    @Binding var showingBookmarks: Bool
    
    let columns = Array(repeating: GridItem(.flexible(), spacing: 20), count: 4)
    
    var body: some View {
        VStack(spacing: 30) {
            // Header
            HStack {
                Text("Bookmarks")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Done") {
                    showingBookmarks = false
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.horizontal, 60)
            .padding(.top, 40)
            
            // Bookmarks grid
            if bookmarkManager.bookmarks.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "heart.slash")
                        .font(.system(size: 60))
                        .foregroundColor(.secondary)
                    
                    Text("No Bookmarks Yet")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("Add bookmarks by tapping the heart icon while browsing")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollView {
                    LazyVGrid(columns: columns, spacing: 30) {
                        ForEach(bookmarkManager.bookmarks) { bookmark in
                            BookmarkTileView(
                                bookmark: bookmark,
                                onTap: {
                                    tabManager.navigateToURL(bookmark.url)
                                    showingBookmarks = false
                                },
                                onDelete: {
                                    bookmarkManager.removeBookmark(bookmark)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 60)
                    .padding(.bottom, 40)
                }
            }
        }
        .background(Color(.systemBackground))
    }
}

struct BookmarkTileView: View {
    let bookmark: Bookmark
    let onTap: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteConfirmation = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Thumbnail or icon
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray5))
                        .frame(height: 120)
                    
                    if let thumbnailData = bookmark.thumbnailData,
                       let uiImage = UIImage(data: thumbnailData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 120)
                            .clipped()
                            .cornerRadius(12)
                    } else {
                        VStack(spacing: 8) {
                            Image(systemName: iconForURL(bookmark.url))
                                .font(.system(size: 30))
                                .foregroundColor(.blue)
                            
                            Text(bookmark.url.host ?? "Website")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }
                }
                
                // Title
                Text(bookmark.title)
                    .font(.headline)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
                
                // URL
                Text(bookmark.url.host ?? bookmark.url.absoluteString)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            .frame(width: 200)
            .padding(16)
            .background(Color(.systemGray6))
            .cornerRadius(16)
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            Button("Delete Bookmark", role: .destructive) {
                showingDeleteConfirmation = true
            }
        }
        .alert("Delete Bookmark", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                onDelete()
            }
        } message: {
            Text("Are you sure you want to delete this bookmark?")
        }
    }
    
    private func iconForURL(_ url: URL) -> String {
        let host = url.host?.lowercased() ?? ""
        
        if host.contains("google") {
            return "magnifyingglass"
        } else if host.contains("youtube") {
            return "play.rectangle"
        } else if host.contains("netflix") {
            return "tv"
        } else if host.contains("apple") {
            return "applelogo"
        } else if host.contains("wikipedia") {
            return "book"
        } else if host.contains("github") {
            return "chevron.left.forwardslash.chevron.right"
        } else if host.contains("twitter") || host.contains("x.com") {
            return "at"
        } else if host.contains("facebook") {
            return "person.2"
        } else if host.contains("instagram") {
            return "camera"
        } else if host.contains("linkedin") {
            return "briefcase"
        } else {
            return "globe"
        }
    }
}
