//
//  BookmarkManager.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct Bookmark: Identifiable, Codable {
    let id = UUID()
    let title: String
    let url: URL
    let dateAdded: Date
    var thumbnailData: Data?
    
    init(title: String, url: URL, thumbnailData: Data? = nil) {
        self.title = title
        self.url = url
        self.dateAdded = Date()
        self.thumbnailData = thumbnailData
    }
}

class BookmarkManager: ObservableObject {
    @Published var bookmarks: [Bookmark] = []
    
    private let userDefaults = UserDefaults.standard
    private let bookmarksKey = "SavedBookmarks"
    
    init() {
        loadBookmarks()
        addDefaultBookmarks()
    }
    
    private func addDefaultBookmarks() {
        if bookmarks.isEmpty {
            let defaultBookmarks = [
                Bookmark(title: "Google", url: URL(string: "https://www.google.com")!),
                Bookmark(title: "YouTube", url: URL(string: "https://www.youtube.com")!),
                Bookmark(title: "Netflix", url: URL(string: "https://www.netflix.com")!),
                Bookmark(title: "Apple", url: URL(string: "https://www.apple.com")!),
                Bookmark(title: "Wikipedia", url: URL(string: "https://www.wikipedia.org")!),
                Bookmark(title: "GitHub", url: URL(string: "https://www.github.com")!)
            ]
            bookmarks = defaultBookmarks
            saveBookmarks()
        }
    }
    
    func addBookmark(title: String, url: URL, thumbnailData: Data? = nil) {
        let bookmark = Bookmark(title: title, url: url, thumbnailData: thumbnailData)
        bookmarks.append(bookmark)
        saveBookmarks()
    }
    
    func removeBookmark(_ bookmark: Bookmark) {
        bookmarks.removeAll { $0.id == bookmark.id }
        saveBookmarks()
    }
    
    func isBookmarked(url: URL) -> Bool {
        return bookmarks.contains { $0.url == url }
    }
    
    func toggleBookmark(title: String, url: URL, thumbnailData: Data? = nil) {
        if let existingBookmark = bookmarks.first(where: { $0.url == url }) {
            removeBookmark(existingBookmark)
        } else {
            addBookmark(title: title, url: url, thumbnailData: thumbnailData)
        }
    }
    
    private func saveBookmarks() {
        if let encoded = try? JSONEncoder().encode(bookmarks) {
            userDefaults.set(encoded, forKey: bookmarksKey)
        }
    }
    
    private func loadBookmarks() {
        if let data = userDefaults.data(forKey: bookmarksKey),
           let decoded = try? JSONDecoder().decode([Bookmark].self, from: data) {
            bookmarks = decoded
        }
    }
}
