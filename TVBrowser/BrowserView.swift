//
//  BrowserView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI
import WebKit

struct BrowserView: UIViewRepresentable {
    @ObservedObject var tabManager: TabManager
    @ObservedObject var bookmarkManager: BookmarkManager
    @ObservedObject var historyManager: HistoryManager
    
    func makeUIView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        webView.allowsBackForwardNavigationGestures = true
        
        // Set up the current tab's web view
        if let currentTab = tabManager.currentTab {
            currentTab.webView = webView
            if let url = currentTab.url {
                let request = URLRequest(url: url)
                webView.load(request)
            }
        }
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        guard let currentTab = tabManager.currentTab else { return }
        
        // If the tab's web view is different, update it
        if currentTab.webView != webView {
            currentTab.webView = webView
        }
        
        // Load URL if it's different from current
        if let url = currentTab.url,
           webView.url != url {
            let request = URLRequest(url: url)
            webView.load(request)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: BrowserView
        
        init(_ parent: BrowserView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            parent.tabManager.updateCurrentTab(
                title: "Loading...",
                url: webView.url,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                isLoading: true
            )
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            let title = webView.title ?? webView.url?.host ?? "Untitled"
            
            parent.tabManager.updateCurrentTab(
                title: title,
                url: webView.url,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                isLoading: false
            )
            
            // Add to history
            if let url = webView.url {
                parent.historyManager.addHistoryItem(title: title, url: url)
            }
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            parent.tabManager.updateCurrentTab(
                title: "Failed to load",
                url: webView.url,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                isLoading: false
            )
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            decisionHandler(.allow)
        }
    }
}
