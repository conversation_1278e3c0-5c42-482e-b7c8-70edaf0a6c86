//
//  ContentView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var tabManager = TabManager()
    @StateObject private var bookmarkManager = BookmarkManager()
    @StateObject private var historyManager = HistoryManager()
    @State private var showingBookmarks = false
    @State private var showingSettings = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Navigation Bar
                NavigationBarView(
                    tabManager: tabManager,
                    bookmarkManager: bookmarkManager,
                    historyManager: historyManager,
                    showingBookmarks: $showingBookmarks,
                    showingSettings: $showingSettings
                )

                // Main Browser Content
                if showingBookmarks {
                    BookmarkGridView(
                        bookmarkManager: bookmarkManager,
                        tabManager: tabManager,
                        showingBookmarks: $showingBookmarks
                    )
                } else {
                    BrowserView(
                        tabManager: tabManager,
                        bookmarkManager: bookmarkManager,
                        historyManager: historyManager
                    )
                }
            }
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
    }
}

#Preview {
    ContentView()
}
