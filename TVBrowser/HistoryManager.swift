//
//  HistoryManager.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct HistoryItem: Identifiable, Codable {
    let id = UUID()
    let title: String
    let url: URL
    let visitDate: Date
    
    init(title: String, url: URL) {
        self.title = title
        self.url = url
        self.visitDate = Date()
    }
}

class HistoryManager: ObservableObject {
    @Published var historyItems: [HistoryItem] = []
    
    private let userDefaults = UserDefaults.standard
    private let historyKey = "BrowsingHistory"
    private let maxHistoryItems = 1000
    
    init() {
        loadHistory()
    }
    
    func addHistoryItem(title: String, url: URL) {
        // Don't add duplicate consecutive entries
        if let lastItem = historyItems.first, lastItem.url == url {
            return
        }
        
        let historyItem = HistoryItem(title: title, url: url)
        historyItems.insert(historyItem, at: 0)
        
        // Limit history size
        if historyItems.count > maxHistoryItems {
            historyItems = Array(historyItems.prefix(maxHistoryItems))
        }
        
        saveHistory()
    }
    
    func clearHistory() {
        historyItems.removeAll()
        saveHistory()
    }
    
    func removeHistoryItem(_ item: HistoryItem) {
        historyItems.removeAll { $0.id == item.id }
        saveHistory()
    }
    
    func getRecentItems(limit: Int = 10) -> [HistoryItem] {
        return Array(historyItems.prefix(limit))
    }
    
    private func saveHistory() {
        if let encoded = try? JSONEncoder().encode(historyItems) {
            userDefaults.set(encoded, forKey: historyKey)
        }
    }
    
    private func loadHistory() {
        if let data = userDefaults.data(forKey: historyKey),
           let decoded = try? JSONDecoder().decode([HistoryItem].self, from: data) {
            historyItems = decoded
        }
    }
}
