//
//  NavigationBarView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct NavigationBarView: View {
    @ObservedObject var tabManager: TabManager
    @ObservedObject var bookmarkManager: BookmarkManager
    @ObservedObject var historyManager: HistoryManager
    @Binding var showingBookmarks: Bool
    @Binding var showingSettings: Bool
    
    @State private var showingURLInput = false
    @State private var urlText = ""
    
    var body: some View {
        HStack(spacing: 20) {
            // Back button
            Button(action: goBack) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(tabManager.currentTab?.canGoBack == true ? .primary : .secondary)
            }
            .disabled(tabManager.currentTab?.canGoBack != true)
            .buttonStyle(PlainButtonStyle())
            
            // Forward button
            Button(action: goForward) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(tabManager.currentTab?.canGoForward == true ? .primary : .secondary)
            }
            .disabled(tabManager.currentTab?.canGoForward != true)
            .buttonStyle(PlainButtonStyle())
            
            // Refresh button
            Button(action: refresh) {
                Image(systemName: tabManager.currentTab?.isLoading == true ? "xmark" : "arrow.clockwise")
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())
            
            // URL/Address bar
            Button(action: { showingURLInput = true }) {
                HStack {
                    Image(systemName: "globe")
                        .foregroundColor(.secondary)
                    
                    Text(displayURL)
                        .lineLimit(1)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Bookmark button
            Button(action: toggleBookmark) {
                Image(systemName: isCurrentPageBookmarked ? "heart.fill" : "heart")
                    .font(.title2)
                    .foregroundColor(isCurrentPageBookmarked ? .red : .primary)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Bookmarks grid button
            Button(action: { showingBookmarks.toggle() }) {
                Image(systemName: "square.grid.3x3")
                    .font(.title2)
                    .foregroundColor(showingBookmarks ? .blue : .primary)
            }
            .buttonStyle(PlainButtonStyle())
            
            // New tab button
            Button(action: addNewTab) {
                Image(systemName: "plus")
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Settings button
            Button(action: { showingSettings = true }) {
                Image(systemName: "gearshape")
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 40)
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
        .sheet(isPresented: $showingURLInput) {
            URLInputView(
                urlText: $urlText,
                isPresented: $showingURLInput,
                onSubmit: navigateToURL
            )
        }
        .onAppear {
            updateURLText()
        }
        .onChange(of: tabManager.currentTab?.url) { _ in
            updateURLText()
        }
    }
    
    private var displayURL: String {
        if let url = tabManager.currentTab?.url {
            return url.absoluteString
        }
        return "Enter URL or search..."
    }
    
    private var isCurrentPageBookmarked: Bool {
        guard let url = tabManager.currentTab?.url else { return false }
        return bookmarkManager.isBookmarked(url: url)
    }
    
    private func goBack() {
        tabManager.currentTab?.webView?.goBack()
    }
    
    private func goForward() {
        tabManager.currentTab?.webView?.goForward()
    }
    
    private func refresh() {
        if tabManager.currentTab?.isLoading == true {
            tabManager.currentTab?.webView?.stopLoading()
        } else {
            tabManager.currentTab?.webView?.reload()
        }
    }
    
    private func toggleBookmark() {
        guard let currentTab = tabManager.currentTab,
              let url = currentTab.url else { return }
        
        bookmarkManager.toggleBookmark(
            title: currentTab.title,
            url: url
        )
    }
    
    private func addNewTab() {
        tabManager.addTab()
    }
    
    private func navigateToURL(_ urlString: String) {
        var finalURLString = urlString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Add https:// if no scheme is provided
        if !finalURLString.hasPrefix("http://") && !finalURLString.hasPrefix("https://") {
            // Check if it looks like a URL (contains a dot)
            if finalURLString.contains(".") && !finalURLString.contains(" ") {
                finalURLString = "https://" + finalURLString
            } else {
                // Treat as search query
                finalURLString = "https://www.google.com/search?q=" + finalURLString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
            }
        }
        
        if let url = URL(string: finalURLString) {
            tabManager.navigateToURL(url)
        }
    }
    
    private func updateURLText() {
        urlText = tabManager.currentTab?.url?.absoluteString ?? ""
    }
}
