//
//  SettingsView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("homepage") private var homepage = "https://www.google.com"
    @AppStorage("searchEngine") private var searchEngine = "Google"
    @AppStorage("clearHistoryOnExit") private var clearHistoryOnExit = false
    @AppStorage("blockPopups") private var blockPopups = true
    @AppStorage("enableJavaScript") private var enableJavaScript = true
    
    let searchEngines = ["Google", "Bing", "DuckDuckGo", "Yahoo"]
    
    var body: some View {
        NavigationView {
            Form {
                Section("General") {
                    HStack {
                        Text("Homepage")
                        Spacer()
                        TextField("Homepage URL", text: $homepage)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(maxWidth: 400)
                    }
                    
                    HStack {
                        Text("Search Engine")
                        Spacer()
                        Picker("Search Engine", selection: $searchEngine) {
                            ForEach(searchEngines, id: \.self) { engine in
                                Text(engine).tag(engine)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .frame(maxWidth: 200)
                    }
                }
                
                Section("Privacy") {
                    Toggle("Clear History on Exit", isOn: $clearHistoryOnExit)
                    Toggle("Block Pop-ups", isOn: $blockPopups)
                    Toggle("Enable JavaScript", isOn: $enableJavaScript)
                }
                
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Build")
                        Spacer()
                        Text("2025.07.23")
                            .foregroundColor(.secondary)
                    }
                }
                
                Section {
                    Button("Reset to Defaults") {
                        resetToDefaults()
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func resetToDefaults() {
        homepage = "https://www.google.com"
        searchEngine = "Google"
        clearHistoryOnExit = false
        blockPopups = true
        enableJavaScript = true
    }
}
