//
//  TabManager.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI
//import WebKit

class Tab: ObservableObject, Identifiable {
    let id = UUID()
    @Published var title: String = "New Tab"
    @Published var url: URL?
    @Published var isLoading: Bool = false
    @Published var canGoBack: Bool = false
    @Published var canGoForward: Bool = false
    var webView: WKWebView?
    
    init(url: URL? = nil) {
        self.url = url
        if let url = url {
            self.title = url.host ?? "New Tab"
        }
    }
}

class TabManager: ObservableObject {
    @Published var tabs: [Tab] = []
    @Published var currentTabIndex: Int = 0
    
    var currentTab: Tab? {
        guard !tabs.isEmpty && currentTabIndex < tabs.count else { return nil }
        return tabs[currentTabIndex]
    }
    
    init() {
        // Create initial tab with homepage
        let homeURL = URL(string: "https://www.google.com")
        addTab(url: homeURL)
    }
    
    func addTab(url: URL? = nil) {
        let newTab = Tab(url: url)
        tabs.append(newTab)
        currentTabIndex = tabs.count - 1
    }
    
    func closeTab(at index: Int) {
        guard index < tabs.count else { return }
        tabs.remove(at: index)
        
        if tabs.isEmpty {
            addTab()
        } else if currentTabIndex >= tabs.count {
            currentTabIndex = tabs.count - 1
        } else if index <= currentTabIndex && currentTabIndex > 0 {
            currentTabIndex -= 1
        }
    }
    
    func switchToTab(at index: Int) {
        guard index < tabs.count else { return }
        currentTabIndex = index
    }
    
    func navigateToURL(_ url: URL) {
        guard let currentTab = currentTab else { return }
        currentTab.url = url
        currentTab.title = url.host ?? "Loading..."
    }
    
    func updateCurrentTab(title: String, url: URL?, canGoBack: Bool, canGoForward: Bool, isLoading: Bool) {
        guard let currentTab = currentTab else { return }
        currentTab.title = title
        currentTab.url = url
        currentTab.canGoBack = canGoBack
        currentTab.canGoForward = canGoForward
        currentTab.isLoading = isLoading
    }
}
