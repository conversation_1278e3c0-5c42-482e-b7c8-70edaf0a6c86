//
//  URLInputView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct URLInputView: View {
    @Binding var urlText: String
    @Binding var isPresented: Bool
    let onSubmit: (String) -> Void
    
    @State private var currentText = ""
    @FocusState private var isTextFieldFocused: Bool
    
    let quickLinks = [
        ("Google", "https://www.google.com"),
        ("YouTube", "https://www.youtube.com"),
        ("Netflix", "https://www.netflix.com"),
        ("Apple", "https://www.apple.com"),
        ("Wikipedia", "https://www.wikipedia.org"),
        ("GitHub", "https://www.github.com")
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Title
                Text("Enter URL or Search")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                // Text input field
                VStack(spacing: 15) {
                    TextField("Enter URL or search term", text: $currentText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.title2)
                        .focused($isTextFieldFocused)
                        .onSubmit {
                            submitURL()
                        }
                    
                    HStack(spacing: 20) {
                        Button("Cancel") {
                            isPresented = false
                        }
                        .buttonStyle(.bordered)
                        
                        Button("Go") {
                            submitURL()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(currentText.isEmpty)
                    }
                }
                .padding(.horizontal, 100)
                
                // Quick links
                VStack(spacing: 20) {
                    Text("Quick Links")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 20) {
                        ForEach(quickLinks, id: \.0) { name, url in
                            Button(action: {
                                currentText = url
                                submitURL()
                            }) {
                                VStack(spacing: 8) {
                                    Image(systemName: iconForSite(name))
                                        .font(.title)
                                        .foregroundColor(.blue)
                                    
                                    Text(name)
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                }
                                .frame(width: 120, height: 80)
                                .background(Color(.systemGray6))
                                .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .padding(.horizontal, 100)
                
                Spacer()
            }
            .padding(.top, 50)
            .onAppear {
                currentText = urlText
                isTextFieldFocused = true
            }
        }
    }
    
    private func submitURL() {
        guard !currentText.isEmpty else { return }
        onSubmit(currentText)
        isPresented = false
    }
    
    private func iconForSite(_ name: String) -> String {
        switch name.lowercased() {
        case "google":
            return "magnifyingglass"
        case "youtube":
            return "play.rectangle"
        case "netflix":
            return "tv"
        case "apple":
            return "applelogo"
        case "wikipedia":
            return "book"
        case "github":
            return "chevron.left.forwardslash.chevron.right"
        default:
            return "globe"
        }
    }
}
